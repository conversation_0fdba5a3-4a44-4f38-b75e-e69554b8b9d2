tableextension 50103 ContactsExtension extends "Contact"
{
    fields
    {
        field(50101; CustomerNo; Code[20])
        {
            Caption = 'Customer No.';
            FieldClass = FlowField;
            CalcFormula = Lookup("Contact Business Relation"."No." WHERE("Contact No." = FIELD("No."), "Business Relation Code" = CONST('CUST')));
        }
        field(50102; Notes; Text[2048])
        {
            Caption = 'Notes';
        }
        field(50100; SalesforceAccountId; Text[50])
        {
            Caption = 'Salesforce Account Id';
        }
    }
}

pageextension 50103 ContactsExtension extends "Contact List"
{
    layout
    {
        addafter("No.")
        {
            field("Date of Last Interaction"; Rec."Date of Last Interaction")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the date of the last interaction.';
            }
            field(Notes; Rec.Notes)
            {
                ApplicationArea = All;
                ToolTip = 'Specifies any additional notes for the contact.';
            }
            field(Type; Rec.Type)
            {
                ApplicationArea = All;
            }
        }
        addafter("Territory Code")
        {
            field(SalesforceAccountId; Rec.SalesforceAccountId)
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the Salesforce Account ID.';
            }
        }
        modify("Job Title")
        {
            Visible = true;
        }
    }
}

pageextension 50104 ContactCardExtension extends "Contact Card"
{
    layout
    {
        addafter("Salesperson Code")
        {
            field(SalesforceAccountId; Rec.SalesforceAccountId)
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the Salesforce Account ID.';
            }
            field(CustomerNo; Rec.CustomerNo)
            {
                ApplicationArea = All;
                Caption = 'Customer No.';
                ToolTip = 'Specifies the Customer No. from the linked Customer Card.';
                Editable = false;
            }
            field(Notes; Rec.Notes)
            {
                ApplicationArea = All;
                Caption = 'Notes';
                ToolTip = 'Specifies any additional notes for the contact.';
                MultiLine = true;
            }
        }
        addafter("General")
        {
            group("Time Bank")
            {
                Caption = 'Bank Hours';
                part(TimeBankLines; "Customer Time Bank Subform")
                {
                    ApplicationArea = All;
                    SubPageLink = "Account__c" = field(CustomerNo);
                }
            }
        }
        modify("Job Title")
        {
            Visible = true;
        }
    }
    actions
    {
        modify("Oppo&rtunities")
        {
            Promoted = true;
            PromotedCategory = Process;
        }
    }
}