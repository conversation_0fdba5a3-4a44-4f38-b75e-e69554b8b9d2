codeunit 50107 "Open Opp Email"
{
    trigger OnRun()
    var
        Opportunity: Record Opportunity;
        TempExcelBuffer: Record "Excel Buffer" temporary;
        FileName: Text;
        RecipientEmail: Text[250];
        EmailSubject: Text[250];
        EmailBody: Text;
        ReportOutStream: OutStream;
        ReportInStream: InStream;
        TempBlob: Codeunit "Temp Blob";
        EmailMessage: Codeunit "Email Message";
        Email: Codeunit Email;
        Sent: Boolean;
        ExcelContentType: Text;
        SalesCycleStage: Record "Sales Cycle Stage";
        StageDescription: Text[100];
        Contact: Record Contact;
        ClientName: Text[100];
    begin
        // --- Configuration ---
        RecipientEmail := '<EMAIL>;<EMAIL>;<EMAIL>'; // Hardcoded recipient emails
        FileName := 'OpenOpportunities_' + Format(Today, 0, '<Year4><Month,2><Day,2>') + '.xlsx'; // Dynamic filename with date
        EmailSubject := 'Open Opportunities - ' + Format(Today);
        EmailBody := 'Please find the attached Open Opportunities Report generated on ' + Format(Today) + '.';
        ExcelContentType := 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'; // MIME type for .xlsx

        // --- Apply the same filters as the page ---
        // Filter for non-closed opportunities
        Opportunity.SetRange("Closed", false);

        // --- Create Excel data ---
        TempExcelBuffer.DeleteAll();

        // Create header row
        TempExcelBuffer.NewRow();
        TempExcelBuffer.AddColumn('No.', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
        TempExcelBuffer.AddColumn('Description', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
        TempExcelBuffer.AddColumn('Type of Opportunity', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
        TempExcelBuffer.AddColumn('Type of Support', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
        TempExcelBuffer.AddColumn('Salesperson Code', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
        TempExcelBuffer.AddColumn('Type of Technology', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
        TempExcelBuffer.AddColumn('Client Name', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
        TempExcelBuffer.AddColumn('Partner', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
        TempExcelBuffer.AddColumn('Sales Cycle Code', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
        TempExcelBuffer.AddColumn('Current Sales Cycle Stage', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
        TempExcelBuffer.AddColumn('Stage Description', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
        TempExcelBuffer.AddColumn('Creation Date', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
        TempExcelBuffer.AddColumn('Estimated Closing Date', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
        TempExcelBuffer.AddColumn('Date Closed', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
        TempExcelBuffer.AddColumn('Amount', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
        TempExcelBuffer.AddColumn('Margin', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
        TempExcelBuffer.AddColumn('Gross Profit', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);

        // Add data rows
        if Opportunity.FindSet() then
            repeat
                // Get stage description
                StageDescription := '';
                if SalesCycleStage.Get(Opportunity."Sales Cycle Code", Opportunity."Current Sales Cycle Stage") then
                    StageDescription := SalesCycleStage.Description;

                // Get client name - use Contact Name if available, otherwise lookup Contact record
                ClientName := '';
                if Opportunity."Contact Name" <> '' then
                    ClientName := Opportunity."Contact Name"
                else
                    if Opportunity."Contact No." <> '' then begin
                        Clear(Contact);
                        if Contact.Get(Opportunity."Contact No.") then
                            ClientName := Contact.Name;
                    end;

                TempExcelBuffer.NewRow();
                TempExcelBuffer.AddColumn(Opportunity."No.", false, '', false, false, false, '', TempExcelBuffer."Cell Type"::Text);
                TempExcelBuffer.AddColumn(Opportunity.Description, false, '', false, false, false, '', TempExcelBuffer."Cell Type"::Text);
                TempExcelBuffer.AddColumn(Opportunity.Type_of_Project_Multi__c, false, '', false, false, false, '', TempExcelBuffer."Cell Type"::Text);
                TempExcelBuffer.AddColumn(Opportunity.Type_Of_Support_Multi__c, false, '', false, false, false, '', TempExcelBuffer."Cell Type"::Text);
                TempExcelBuffer.AddColumn(Opportunity."Salesperson Code", false, '', false, false, false, '', TempExcelBuffer."Cell Type"::Text);
                TempExcelBuffer.AddColumn(Opportunity.Type_of_Technology__c, false, '', false, false, false, '', TempExcelBuffer."Cell Type"::Text);
                TempExcelBuffer.AddColumn(ClientName, false, '', false, false, false, '', TempExcelBuffer."Cell Type"::Text);
                TempExcelBuffer.AddColumn(Opportunity.Partner_For_The_Project__c, false, '', false, false, false, '', TempExcelBuffer."Cell Type"::Text);
                TempExcelBuffer.AddColumn(Opportunity."Sales Cycle Code", false, '', false, false, false, '', TempExcelBuffer."Cell Type"::Text);
                TempExcelBuffer.AddColumn(Format(Opportunity."Current Sales Cycle Stage"), false, '', false, false, false, '', TempExcelBuffer."Cell Type"::Text);
                TempExcelBuffer.AddColumn(StageDescription, false, '', false, false, false, '', TempExcelBuffer."Cell Type"::Text);
                TempExcelBuffer.AddColumn(Format(Opportunity."Creation Date"), false, '', false, false, false, '', TempExcelBuffer."Cell Type"::Text);
                TempExcelBuffer.AddColumn(Format(Opportunity."Estimated Closing Date"), false, '', false, false, false, '', TempExcelBuffer."Cell Type"::Text);
                TempExcelBuffer.AddColumn(Format(Opportunity."Date Closed"), false, '', false, false, false, '', TempExcelBuffer."Cell Type"::Text);
                TempExcelBuffer.AddColumn(Format(Opportunity.Amount), false, '', false, false, false, '', TempExcelBuffer."Cell Type"::Number);
                TempExcelBuffer.AddColumn(Format(Opportunity."Margin_Amount__c"), false, '', false, false, false, '', TempExcelBuffer."Cell Type"::Number);
                TempExcelBuffer.AddColumn(Format(Opportunity.GrossProfit), false, '', false, false, false, '', TempExcelBuffer."Cell Type"::Number);
            until Opportunity.Next() = 0;

        // --- Create Excel file and send email ---
        TempBlob.CreateOutStream(ReportOutStream);
        TempExcelBuffer.CreateNewBook('Open Opportunities');
        TempExcelBuffer.WriteSheet('Open Opportunities', CompanyName, UserId);
        TempExcelBuffer.CloseBook();
        TempExcelBuffer.SetFriendlyFilename(FileName);
        TempExcelBuffer.SaveToStream(ReportOutStream, true);

        // Create an InStream from the TempBlob to read the data for attachment
        TempBlob.CreateInStream(ReportInStream);

        // Create the email message
        EmailMessage.Create(RecipientEmail, EmailSubject, EmailBody);

        // Add the Excel stream as an attachment
        EmailMessage.AddAttachment(FileName, ExcelContentType, ReportInStream);

        // Send the email using the standard Email codeunit
        Sent := Email.Send(EmailMessage);

        // --- Feedback ---
        if Sent then
            Message('Excel file "%1" successfully sent to %2.', FileName, RecipientEmail)
        else
            Message('Failed to send the email for file "%1" to %2. Check the Email Outbox/Sent Items and BC email configuration.', FileName, RecipientEmail);
    end;
}