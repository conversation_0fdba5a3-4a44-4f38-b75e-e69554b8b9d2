page 50135 "Opportunity Type Selection"
{
    PageType = Worksheet;
    ApplicationArea = All;
    UsageCategory = Lists;
    SourceTable = "Opportunity Type";
    SourceTableTemporary = true;
    Caption = 'Select Opportunity Types';
    InsertAllowed = false;
    DeleteAllowed = false;

    layout
    {
        area(Content)
        {
            repeater(GroupName)
            {
                field(Selected; Rec.Selected)
                {
                    ApplicationArea = All;
                    Caption = 'Select';
                    trigger OnValidate()
                    begin
                        CurrPage.Update();
                    end;
                }
                field(Description; Rec.Description)
                {
                    ApplicationArea = All;
                    Caption = 'Opportunity Type';
                    Editable = false;
                }
            }
        }
    }

    actions
    {
        area(Processing)
        {
            action(SelectAll)
            {
                Caption = 'Select All';
                ApplicationArea = All;
                trigger OnAction()
                begin
                    if Rec.FindSet() then
                        repeat
                            Rec.Selected := true;
                            Rec.Modify();
                        until Rec.Next() = 0;
                    CurrPage.Update();
                end;
            }
            action(ClearAll)
            {
                Caption = 'Clear All';
                ApplicationArea = All;
                trigger OnAction()
                begin
                    if Rec.FindSet() then
                        repeat
                            Rec.Selected := false;
                            Rec.Modify();
                        until Rec.Next() = 0;
                    CurrPage.Update();
                end;
            }
        }
    }

    procedure SetTempRecord(var TempOpportunityType: Record "Opportunity Type" temporary)
    begin
        Rec.Reset();
        Rec.DeleteAll();

        if TempOpportunityType.FindSet() then
            repeat
                Rec := TempOpportunityType;
                if not Rec.Insert() then
                    Rec.Modify();
            until TempOpportunityType.Next() = 0;

        Rec.Reset();
        CurrPage.Update(false);
        CurrPage.Editable := true;
    end;

    procedure GetSelectedTypes(var TempOpportunityType: Record "Opportunity Type" temporary)
    begin
        TempOpportunityType.Reset();
        TempOpportunityType.DeleteAll();

        Rec.Reset();
        if Rec.FindSet() then
            repeat
                if Rec.Selected then begin
                    TempOpportunityType.Init();
                    TempOpportunityType := Rec;
                    TempOpportunityType.Insert();
                end;
            until Rec.Next() = 0;
    end;
}
