// Simple test page for Contact Salesperson Update
page 50111 "Contact SP Update Simple"
{
    PageType = Card;
    ApplicationArea = All;
    UsageCategory = Administration;
    Caption = 'Contact Salesperson Update (Simple)';

    layout
    {
        area(Content)
        {
            group(Selection)
            {
                Caption = 'Salesperson Selection';
                
                field(SalespersonCode; SalespersonCode)
                {
                    ApplicationArea = All;
                    Caption = 'Salesperson Code';
                    ToolTip = 'Select the salesperson whose contacts will be updated.';
                    TableRelation = "Salesperson/Purchaser";
                    
                    trigger OnValidate()
                    var
                        SalespersonPurchaser: Record "Salesperson/Purchaser";
                    begin
                        if SalespersonCode <> '' then begin
                            if SalespersonPurchaser.Get(SalespersonCode) then
                                SalespersonName := SalespersonPurchaser.Name
                            else
                                SalespersonName := '';
                        end else
                            SalespersonName := '';
                    end;
                }
                
                field(SalespersonName; SalespersonName)
                {
                    ApplicationArea = All;
                    Caption = 'Salesperson Name';
                    ToolTip = 'Shows the name of the selected salesperson.';
                    Editable = false;
                }
            }
            
            group(Instructions)
            {
                Caption = 'Instructions';
                
                field(InstructionText; InstructionText)
                {
                    ApplicationArea = All;
                    Caption = '';
                    ToolTip = 'Instructions for using this utility.';
                    Editable = false;
                    MultiLine = true;
                    ShowCaption = false;
                }
            }
        }
    }

    actions
    {
        area(Processing)
        {
            action(TestAction)
            {
                ApplicationArea = All;
                Caption = 'Test Action';
                ToolTip = 'Test action to verify the page works.';
                Image = TestFile;
                
                trigger OnAction()
                begin
                    Message('Test action executed successfully. Salesperson: %1', SalespersonCode);
                end;
            }
        }
    }

    var
        SalespersonCode: Code[20];
        SalespersonName: Text[100];
        InstructionText: Text;

    trigger OnOpenPage()
    begin
        InstructionText := 'This is a simple test version of the Contact Salesperson Update utility.' + '\' +
                          'If this page opens without crashing, the issue is with the more complex version.';
    end;
}
