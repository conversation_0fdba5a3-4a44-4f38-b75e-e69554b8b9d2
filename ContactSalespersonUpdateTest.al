// Test page without temporary table
page 50112 "Contact SP Update Test"
{
    PageType = Card;
    ApplicationArea = All;
    UsageCategory = Administration;
    Caption = 'Contact Salesperson Update (Test)';

    layout
    {
        area(Content)
        {
            group(Selection)
            {
                Caption = 'Salesperson Selection';
                
                field(SalespersonCode; SalespersonCode)
                {
                    ApplicationArea = All;
                    Caption = 'Salesperson Code';
                    ToolTip = 'Select the salesperson whose contacts will be updated.';
                    TableRelation = "Salesperson/Purchaser";
                    
                    trigger OnValidate()
                    var
                        SalespersonPurchaser: Record "Salesperson/Purchaser";
                    begin
                        if SalespersonCode <> '' then begin
                            if SalespersonPurchaser.Get(SalespersonCode) then
                                SalespersonName := SalespersonPurchaser.Name
                            else
                                SalespersonName := '';
                        end else
                            SalespersonName := '';
                    end;
                }
                
                field(SalespersonName; SalespersonName)
                {
                    ApplicationArea = All;
                    Caption = 'Salesperson Name';
                    ToolTip = 'Shows the name of the selected salesperson.';
                    Editable = false;
                }
            }
            
            group(Results)
            {
                Caption = 'Results';
                
                field(ResultText; ResultText)
                {
                    ApplicationArea = All;
                    Caption = 'Result';
                    ToolTip = 'Results of operations.';
                    Editable = false;
                    MultiLine = true;
                }
            }
        }
    }

    actions
    {
        area(Processing)
        {
            action(TestExcelImport)
            {
                ApplicationArea = All;
                Caption = 'Test Excel Import';
                ToolTip = 'Test Excel import functionality.';
                Image = ImportExcel;
                
                trigger OnAction()
                var
                    TempExcelBuffer: Record "Excel Buffer" temporary;
                    FileInStream: InStream;
                    FileName: Text;
                    SheetName: Text;
                begin
                    // Upload file
                    if not UploadIntoStream('Import Excel File', '', 'Excel Files (*.xlsx)|*.xlsx|Excel Files (*.xls)|*.xls', FileName, FileInStream) then begin
                        ResultText := 'File upload cancelled.';
                        exit;
                    end;
                    
                    // Try to import
                    TempExcelBuffer.DeleteAll();
                    SheetName := TempExcelBuffer.SelectSheetsNameStream(FileInStream);
                    TempExcelBuffer.OpenBookStream(FileInStream, SheetName);
                    TempExcelBuffer.ReadSheet();
                    
                    ResultText := StrSubstNo('Excel file imported successfully. Sheet: %1', SheetName);
                    CurrPage.Update(false);
                end;
            }
            
            action(TestContactUpdate)
            {
                ApplicationArea = All;
                Caption = 'Test Contact Update';
                ToolTip = 'Test contact update functionality.';
                Image = Process;
                
                trigger OnAction()
                var
                    Contact: Record Contact;
                    ContactCount: Integer;
                begin
                    if SalespersonCode = '' then begin
                        ResultText := 'Please select a salesperson first.';
                        exit;
                    end;
                    
                    // Count contacts for this salesperson
                    Contact.SetRange("Salesperson Code", SalespersonCode);
                    ContactCount := Contact.Count();
                    
                    ResultText := StrSubstNo('Found %1 contacts for salesperson %2', ContactCount, SalespersonCode);
                    CurrPage.Update(false);
                end;
            }
        }
    }

    var
        SalespersonCode: Code[20];
        SalespersonName: Text[100];
        ResultText: Text;
}
