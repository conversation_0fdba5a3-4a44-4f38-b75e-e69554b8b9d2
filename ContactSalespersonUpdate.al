// Temporary table to hold Excel data during import
table 50110 "Contact Update Buffer"
{
    TableType = Temporary;
    Caption = 'Contact Update Buffer';

    fields
    {
        field(1; "Line No."; Integer)
        {
            Caption = 'Line No.';
            AutoIncrement = true;
        }
        field(2; "Customer Name"; Text[100])
        {
            Caption = 'Customer Name';
        }
    }

    keys
    {
        key(PK; "Line No.")
        {
            Clustered = true;
        }
    }
}

// Main page for Contact Salesperson Update utility
page 50110 "Contact Salesperson Update"
{
    PageType = Card;
    ApplicationArea = All;
    UsageCategory = Administration;
    Caption = 'Contact Salesperson Update';
    SourceTable = "Contact Update Buffer";
    SourceTableTemporary = true;
    InsertAllowed = false;
    ModifyAllowed = false;
    DeleteAllowed = false;

    layout
    {
        area(Content)
        {
            group(Selection)
            {
                Caption = 'Salesperson Selection';

                field(SalespersonCode; SalespersonCode)
                {
                    ApplicationArea = All;
                    Caption = 'Salesperson Code';
                    ToolTip = 'Select the salesperson whose contacts will be updated.';
                    TableRelation = "Salesperson/Purchaser";

                    trigger OnValidate()
                    var
                        SalespersonPurchaser: Record "Salesperson/Purchaser";
                    begin
                        if SalespersonCode <> '' then begin
                            SalespersonPurchaser.Get(SalespersonCode);
                            SalespersonName := SalespersonPurchaser.Name;
                        end else
                            SalespersonName := '';
                    end;
                }

                field(SalespersonName; SalespersonName)
                {
                    ApplicationArea = All;
                    Caption = 'Salesperson Name';
                    ToolTip = 'Shows the name of the selected salesperson.';
                    Editable = false;
                }
            }

            group(Instructions)
            {
                Caption = 'Instructions';

                field(InstructionText; InstructionText)
                {
                    ApplicationArea = All;
                    Caption = '';
                    ToolTip = 'Instructions for using this utility.';
                    Editable = false;
                    MultiLine = true;
                    ShowCaption = false;
                }
            }

            group(ImportedData)
            {
                Caption = 'Imported Customer Names';
                Visible = HasImportedData;

                repeater(CustomerNames)
                {
                    field("Customer Name"; Rec."Customer Name")
                    {
                        ApplicationArea = All;
                        ToolTip = 'Customer name from the Excel file.';
                    }
                }
            }

            group(Results)
            {
                Caption = 'Processing Results';
                Visible = HasResults;

                field(ResultText; ResultText)
                {
                    ApplicationArea = All;
                    Caption = '';
                    ToolTip = 'Results of the contact update process.';
                    Editable = false;
                    MultiLine = true;
                    ShowCaption = false;
                }
            }
        }
    }

    actions
    {
        area(Processing)
        {
            action(ImportExcel)
            {
                ApplicationArea = All;
                Caption = 'Import Excel File';
                ToolTip = 'Import customer names from an Excel file. The file must contain a column named "Customer Name".';
                Image = ImportExcel;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;

                trigger OnAction()
                var
                    ContactUpdateMgt: Codeunit "Contact Salesperson Update Mgt";
                begin
                    if ContactUpdateMgt.ImportExcelFile(Rec) then begin
                        HasImportedData := not Rec.IsEmpty();
                        CurrPage.Update(false);
                    end;
                end;
            }

            action(ProcessUpdate)
            {
                ApplicationArea = All;
                Caption = 'Process Contact Updates';
                ToolTip = 'Update contact salesperson codes based on the imported customer names.';
                Image = Process;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Enabled = HasImportedData and (SalespersonCode <> '');

                trigger OnAction()
                var
                    ContactUpdateMgt: Codeunit "Contact Salesperson Update Mgt";
                begin
                    if SalespersonCode = '' then
                        Error('Please select a salesperson before processing updates.');

                    if Rec.IsEmpty() then
                        Error('Please import an Excel file with customer names before processing updates.');

                    if Confirm('This will update contacts for salesperson %1. Continue?', false, SalespersonCode) then begin
                        ResultText := ContactUpdateMgt.ProcessContactUpdates(Rec, SalespersonCode);
                        HasResults := true;
                        CurrPage.Update(false);
                    end;
                end;
            }

            action(ClearData)
            {
                ApplicationArea = All;
                Caption = 'Clear Imported Data';
                ToolTip = 'Clear the imported customer names to start over.';
                Image = ClearLog;

                trigger OnAction()
                begin
                    if Confirm('Clear all imported data?', false) then begin
                        Rec.DeleteAll();
                        HasImportedData := false;
                        HasResults := false;
                        ResultText := '';
                        CurrPage.Update(false);
                    end;
                end;
            }
        }
    }

    var
        SalespersonCode: Code[20];
        SalespersonName: Text[100];
        InstructionText: Text;
        ResultText: Text;
        HasImportedData: Boolean;
        HasResults: Boolean;

    trigger OnOpenPage()
    begin
        InstructionText := 'This utility updates contact salesperson assignments based on an Excel file.' + '\' +
                          '1. Select a salesperson from the dropdown above.' + '\' +
                          '2. Import an Excel file containing a "Customer Name" column.' + '\' +
                          '3. Process the updates - contacts with matching company names will be assigned to the selected salesperson.' + '\' +
                          '4. All other contacts currently assigned to this salesperson will be unassigned.' + '\' +
                          '\' +
                          'IMPORTANT: This will modify existing contact assignments. Make sure to backup your data before proceeding.';
    end;
}
