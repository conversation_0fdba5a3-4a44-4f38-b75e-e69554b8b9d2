page 50134 "Support Type Selection"
{
    PageType = Worksheet;
    ApplicationArea = All;
    UsageCategory = Lists;
    SourceTable = "Support Type";
    SourceTableTemporary = true;
    Caption = 'Select Support Types';
    InsertAllowed = false;
    DeleteAllowed = false;

    layout
    {
        area(Content)
        {
            repeater(GroupName)
            {
                field(Selected; Rec.Selected)
                {
                    ApplicationArea = All;
                    Caption = 'Select';
                    trigger OnValidate()
                    begin
                        CurrPage.Update();
                    end;
                }
                field(Description; Rec.Description)
                {
                    ApplicationArea = All;
                    Caption = 'Support Type';
                    Editable = false;
                }
            }
        }
    }

    actions
    {
        area(Processing)
        {
            action(SelectAll)
            {
                Caption = 'Select All';
                ApplicationArea = All;
                trigger OnAction()
                begin
                    if Rec.FindSet() then
                        repeat
                            Rec.Selected := true;
                            Rec.Modify();
                        until Rec.Next() = 0;
                    CurrPage.Update();
                end;
            }
            action(ClearAll)
            {
                Caption = 'Clear All';
                ApplicationArea = All;
                trigger OnAction()
                begin
                    if Rec.FindSet() then
                        repeat
                            Rec.Selected := false;
                            Rec.Modify();
                        until Rec.Next() = 0;
                    CurrPage.Update();
                end;
            }
        }
    }

    procedure SetTempRecord(var TempSupportType: Record "Support Type" temporary)
    begin
        Rec.Reset();
        Rec.DeleteAll();

        if TempSupportType.FindSet() then
            repeat
                Rec := TempSupportType;
                if not Rec.Insert() then
                    Rec.Modify();
            until TempSupportType.Next() = 0;

        Rec.Reset();
        CurrPage.Update(false);
        CurrPage.Editable := true;
    end;

    procedure GetSelectedTypes(var TempSupportType: Record "Support Type" temporary)
    begin
        TempSupportType.Reset();
        TempSupportType.DeleteAll();

        Rec.Reset();
        if Rec.FindSet() then
            repeat
                if Rec.Selected then begin
                    TempSupportType.Init();
                    TempSupportType := Rec;
                    TempSupportType.Insert();
                end;
            until Rec.Next() = 0;
    end;
}
