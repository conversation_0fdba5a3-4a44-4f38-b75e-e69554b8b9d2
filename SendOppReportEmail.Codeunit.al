codeunit 50105 "Weekly Closed Opp Email"
{
    trigger OnRun()
    var
        Opportunity: Record Opportunity;
        TempExcelBuffer: Record "Excel Buffer" temporary;
        FileName: Text;
        RecipientEmail: Text[250];
        EmailSubject: Text[250];
        EmailBody: Text;
        ReportOutStream: OutStream;
        ReportInStream: InStream;
        TempBlob: Codeunit "Temp Blob";
        EmailMessage: Codeunit "Email Message";
        Email: Codeunit Email;
        Sent: Boolean;
        ExcelContentType: Text;
        StartDate: Date;
        Today: Date;
        LastSat: Date;
        RowNo: Integer;
        Contact: Record Contact;
        ClientName: Text[100];
    begin
        // --- Configuration ---
        RecipientEmail := '<EMAIL>;<EMAIL>;<EMAIL>'; // Hardcoded recipient emails
        FileName := 'WeeklyWonOpportunities_' + Format(Today, 0, '<Year4><Month,2><Day,2>') + '.xlsx'; // Dynamic filename with date
        EmailSubject := 'Weekly Won Opportunities - ' + Format(Today);
        EmailBody := 'Please find the attached Won Opportunities Report generated on ' + Format(Today) + '.';
        ExcelContentType := 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'; // MIME type for .xlsx

        // --- Apply the same filters as the page ---
        // Filter for closed Won opportunities
        Opportunity.SetRange("Status", Opportunity.Status::Won);

        // Calculate last Saturday (2 days before the start of current week)
        LastSat := CalcDate('<-2D>', CalcDate('<-CW>', WorkDate()));

        // Set start date to last Friday
        StartDate := LastSat;

        // Set end date to today
        Today := WorkDate();

        // Filter for Date Closed between start date and today
        Opportunity.SetRange("Date Closed", StartDate, Today);

        // --- Create Excel data ---
        TempExcelBuffer.DeleteAll();

        // Create header row
        RowNo := 1;
        TempExcelBuffer.NewRow();
        TempExcelBuffer.AddColumn('No.', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
        TempExcelBuffer.AddColumn('Description', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
        TempExcelBuffer.AddColumn('Type of Opportunity', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
        TempExcelBuffer.AddColumn('Type of Support', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
        TempExcelBuffer.AddColumn('Salesperson Code', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
        TempExcelBuffer.AddColumn('Type of Technology', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
        TempExcelBuffer.AddColumn('Client Name', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
        TempExcelBuffer.AddColumn('Partner', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
        TempExcelBuffer.AddColumn('Sales Cycle Code', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
        TempExcelBuffer.AddColumn('Creation Date', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
        TempExcelBuffer.AddColumn('Date Closed', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
        TempExcelBuffer.AddColumn('Amount', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
        TempExcelBuffer.AddColumn('Margin', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
        TempExcelBuffer.AddColumn('Gross Profit', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);

        // Add data rows
        if Opportunity.FindSet() then
            repeat
                // Get client name - use Contact Name if available, otherwise lookup Contact record
                ClientName := '';
                if Opportunity."Contact Name" <> '' then
                    ClientName := Opportunity."Contact Name"
                else
                    if Opportunity."Contact No." <> '' then begin
                        Clear(Contact);
                        if Contact.Get(Opportunity."Contact No.") then
                            ClientName := Contact.Name;
                    end;

                TempExcelBuffer.NewRow();
                TempExcelBuffer.AddColumn(Opportunity."No.", false, '', false, false, false, '', TempExcelBuffer."Cell Type"::Text);
                TempExcelBuffer.AddColumn(Opportunity.Description, false, '', false, false, false, '', TempExcelBuffer."Cell Type"::Text);
                TempExcelBuffer.AddColumn(Opportunity.Type_of_Project_Multi__c, false, '', false, false, false, '', TempExcelBuffer."Cell Type"::Text);
                TempExcelBuffer.AddColumn(Opportunity.Type_Of_Support_Multi__c, false, '', false, false, false, '', TempExcelBuffer."Cell Type"::Text);
                TempExcelBuffer.AddColumn(Opportunity."Salesperson Code", false, '', false, false, false, '', TempExcelBuffer."Cell Type"::Text);
                TempExcelBuffer.AddColumn(Opportunity.Type_of_Technology__c, false, '', false, false, false, '', TempExcelBuffer."Cell Type"::Text);
                TempExcelBuffer.AddColumn(ClientName, false, '', false, false, false, '', TempExcelBuffer."Cell Type"::Text);
                TempExcelBuffer.AddColumn(Opportunity.Partner_For_The_Project__c, false, '', false, false, false, '', TempExcelBuffer."Cell Type"::Text);
                TempExcelBuffer.AddColumn(Opportunity."Sales Cycle Code", false, '', false, false, false, '', TempExcelBuffer."Cell Type"::Text);
                TempExcelBuffer.AddColumn(Format(Opportunity."Creation Date"), false, '', false, false, false, '', TempExcelBuffer."Cell Type"::Text);
                TempExcelBuffer.AddColumn(Format(Opportunity."Date Closed"), false, '', false, false, false, '', TempExcelBuffer."Cell Type"::Text);
                TempExcelBuffer.AddColumn(Format(Opportunity.Amount), false, '', false, false, false, '', TempExcelBuffer."Cell Type"::Number);
                TempExcelBuffer.AddColumn(Format(Opportunity."Margin_Amount__c"), false, '', false, false, false, '', TempExcelBuffer."Cell Type"::Number);
                TempExcelBuffer.AddColumn(Format(Opportunity.GrossProfit), false, '', false, false, false, '', TempExcelBuffer."Cell Type"::Number);
            until Opportunity.Next() = 0;

        // --- Create Excel file and send email ---
        TempBlob.CreateOutStream(ReportOutStream);
        TempExcelBuffer.CreateNewBook('Weekly Won Opportunities');
        TempExcelBuffer.WriteSheet('Won Opportunities', CompanyName, UserId);
        TempExcelBuffer.CloseBook();
        TempExcelBuffer.SetFriendlyFilename(FileName);
        TempExcelBuffer.SaveToStream(ReportOutStream, true);

        // Create an InStream from the TempBlob to read the data for attachment
        TempBlob.CreateInStream(ReportInStream);

        // Create the email message
        EmailMessage.Create(RecipientEmail, EmailSubject, EmailBody);

        // Add the Excel stream as an attachment
        EmailMessage.AddAttachment(FileName, ExcelContentType, ReportInStream);

        // Send the email using the standard Email codeunit
        Sent := Email.Send(EmailMessage);

        // --- Feedback ---
        if Sent then
            Message('Excel file "%1" successfully sent to %2.', FileName, RecipientEmail)
        else
            Message('Failed to send the email for file "%1" to %2. Check the Email Outbox/Sent Items and BC email configuration.', FileName, RecipientEmail);
    end;
}