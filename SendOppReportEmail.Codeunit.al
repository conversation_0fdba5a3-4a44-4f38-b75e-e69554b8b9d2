codeunit 50105 "Weekly Closed Opp Email"
{
    trigger OnRun()
    var
        ReportID: Integer;
        FileName: Text;
        RecipientEmail: Text[250];
        EmailSubject: Text[250];
        EmailBody: Text;
        ReportOutStream: OutStream;
        ReportInStream: InStream;
        TempBlob: Codeunit "Temp Blob";
        EmailMessage: Codeunit "Email Message";
        Email: Codeunit Email;
        Sent: Boolean;
        ExcelContentType: Text;
    begin
        // --- Configuration ---
        ReportID := 50101; // Your custom report ID
        RecipientEmail := '<EMAIL>'; // Hardcoded recipient email
        FileName := 'WeeklyWonOpportunities_' + Format(Today, 0, '<Year4><Month,2><Day,2>') + '.xlsx'; // Dynamic filename with date
        EmailSubject := 'Weekly Won Opportunities - ' + Format(Today);
        EmailBody := 'Please find the attached Won Opportunities Report generated on ' + Format(Today) + '.';
        ExcelContentType := 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'; // MIME type for .xlsx

        // --- Process ---
        // Create a temporary blob to store the report output
        TempBlob.CreateOutStream(ReportOutStream);

        // Run the report and save it as Excel to the OutStream
        // Ensure the ReportFormat matches your report's rendering section (Excel)
        Commit(); // Commit any pending changes before running the report if necessary
        if Report.SaveAs(ReportID, '', ReportFormat::Excel, ReportOutStream) then begin
            // Create an InStream from the TempBlob to read the data for attachment
            TempBlob.CreateInStream(ReportInStream);

            // Create the email message
            EmailMessage.Create(RecipientEmail, EmailSubject, EmailBody);

            // Add the report stream as an attachment using the correct content type
            EmailMessage.AddAttachment(FileName, ExcelContentType, ReportInStream);

            // Send the email using the standard Email codeunit
            // This uses the default email scenario configured in BC
            Sent := Email.Send(EmailMessage);

            // --- Feedback ---
            if Sent then
                Message('Report "%1" successfully sent to %2.', FileName, RecipientEmail)
            else
                // Consider more robust logging or error handling here
                Message('Failed to send the email for report "%1" to %2. Check the Email Outbox/Sent Items and BC email configuration.', FileName, RecipientEmail);
        end else begin
            Error('Failed to generate report %1. Please check the report definition and data.', ReportID);
        end;
    end;
}