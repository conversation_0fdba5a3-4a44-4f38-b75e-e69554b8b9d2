pageextension 50109 "Opportunity List Newest First" extends "Opportunity List"
{
    trigger OnOpenPage()
    var
        Opportunity: Record Opportunity;
    begin
        // Clear any existing filters/sorts
        Opportunity.Reset();

        // Set the sorting key
        Opportunity.SetCurrentKey("Creation Date");
        Opportunity.Ascending(false); // Newest first

        // Apply the sort to the page's record
        Rec.SetCurrentKey("Creation Date");
        Rec.Ascending(false); // Newest first

        // Refresh the view
        if Rec.FindSet() then;
    end;
}