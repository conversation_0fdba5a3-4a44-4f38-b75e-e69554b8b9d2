{"version": "0.2.0", "configurations": [{"name": "Microsoft cloud sandbox", "request": "launch", "type": "al", "environmentType": "Sandbox", "environmentName": "Sandbox_2", "startupObjectId": 22, "startupObjectType": "Page", "schemaUpdateMode": "ForceSync", "breakOnError": "All", "launchBrowser": true, "enableLongRunningSqlStatements": true, "enableSqlInformationDebugger": true}, {"name": "AL: Generated Download Symbols request", "request": "launch", "type": "al", "environmentType": "Sandbox", "environmentName": "Sandbox_2", "tenant": "16301eb2-fd3c-424b-811a-76b56ecb8d6b"}, {"name": "AL: Generated Snapshot request", "request": "snapshotInitialize", "type": "al", "environmentType": "Sandbox", "environmentName": "Sandbox_2", "sessionId": 188870, "breakOnNext": "WebClient", "executionContext": "DebugAndProfile", "tenant": "16301eb2-fd3c-424b-811a-76b56ecb8d6b"}]}