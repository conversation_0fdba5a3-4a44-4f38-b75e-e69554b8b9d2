// This extension affects the business chart used for the sales pipeline
// Since we cannot directly extend the sales pipeline chart, we'll modify the opportunity list
// that feeds into the chart to use Status filter instead of Sales Cycle

pageextension 50114 "Sales Pipeline Filter Ext" extends "Opportunity List" // Page 5126
{
    // Add a new action to apply the Status filter for the pipeline chart
    actions
    {
        addlast(Processing)
        {
            action("Filter Pipeline by Status")
            {
                ApplicationArea = All;
                Caption = 'Filter Pipeline by In Progress Status';
                ToolTip = 'Changes the pipeline view to filter by Status: In Progress instead of Sales Cycle';
                Image = FilterLines;
                Promoted = true;
                PromotedCategory = Process;

                trigger OnAction()
                begin
                    Rec.Reset();
                    Rec.SetRange("Sales Cycle Code", ''); // Clear sales cycle filter
                    Rec.SetRange(Status, Rec.Status::"In Progress");
                    CurrPage.Update(false);
                end;
            }
        }
    }

    // Automatically apply filter on page open
    trigger OnOpenPage()
    begin
        // Don't apply automatically to avoid conflicts with other extensions
        // User can click the action to apply the filter when needed
    end;
}

// This extends the Sales Pipeline Chart to filter by Status instead of Sales Cycle
pageextension 50121 "Sales Pipeline Chart Ext" extends "Sales Pipeline Chart" // Page 781
{
    // The Sales Pipeline Chart uses a codeunit to populate the chart
    // We'll subscribe to the relevant events to change the filtering behavior

    trigger OnOpenPage()
    var
        PipelineMgt: Codeunit "Sales Pipeline Chart Mgt.";
        SalesCycle: Record "Sales Cycle";
    begin
        // We'll handle filtering in our event subscriber
    end;
}

// This codeunit subscribes to events in the Sales Pipeline Chart Management codeunit
// to modify the filtering behavior
codeunit 50125 "Pipeline Status Filter"
{
    // Subscribe to the event before setting the default sales cycle
    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Sales Pipeline Chart Mgt.", 'OnBeforeSetDefaultSalesCycle', '', false, false)]
    local procedure OnBeforeSetDefaultSalesCycle(var SalesCycle: Record "Sales Cycle")
    begin
        // Clear the default EXISTING filter that would be applied
        // We'll apply our own filter later
    end;

    // Subscribe to event before getting opportunity entry count
    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Sales Pipeline Chart Mgt.", 'OnGetOppEntryCountOnBeforeCount', '', false, false)]
    local procedure OnGetOppEntryCountOnBeforeCount(var OppEntry: Record "Opportunity Entry")
    begin
        // Clear any existing Sales Cycle filter
        OppEntry.SetRange("Sales Cycle Code");

        // Apply the Status filter instead
        OppEntry.SetRange(Status, OppEntry.Status::"In Progress");
    end;

    // Subscribe to event before drilling down
    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Sales Pipeline Chart Mgt.", 'OnBeforeDrillDown', '', false, false)]
    local procedure OnBeforeDrillDown(var OppEntry: Record "Opportunity Entry")
    begin
        // Clear any existing Sales Cycle filter
        OppEntry.SetRange("Sales Cycle Code");

        // Apply the Status filter instead
        OppEntry.SetRange(Status, OppEntry.Status::"In Progress");
    end;
}