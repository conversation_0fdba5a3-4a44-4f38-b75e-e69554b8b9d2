pageextension 50111 OppExtAttachments extends "Opportunity Card"
{
    layout
    {
        addfirst(FactBoxes)
        {
            part("Attached Documents"; "Doc. Attachment List Factbox")
            {
                ApplicationArea = All;
                Caption = 'Attachments';
                SubPageLink = "Table ID" = CONST(DATABASE::Opportunity),
                              "No." = FIELD("No.");
            }
            systempart(PyamentTermsLinks; Links)
            {
                ApplicationArea = RecordLinks;
            }
        }
    }
}