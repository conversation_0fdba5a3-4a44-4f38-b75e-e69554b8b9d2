page 50136 "Migrate Opportunity Fields"
{
    PageType = Card;
    ApplicationArea = All;
    UsageCategory = Administration;
    Caption = 'Migrate Opportunity Fields';
    
    layout
    {
        area(Content)
        {
            group(Information)
            {
                Caption = 'Migration Information';
                
                field(InfoText; InfoText)
                {
                    ApplicationArea = All;
                    Caption = 'Description';
                    Editable = false;
                    MultiLine = true;
                    ShowCaption = false;
                }
            }
            
            group(MigrationDetails)
            {
                Caption = 'What will be migrated';
                
                field(Migration1; 'Type of Opportunity → Type of Opportunity (Multi)')
                {
                    ApplicationArea = All;
                    Editable = false;
                    ShowCaption = false;
                }
                
                field(Migration2; 'Type of Support → Type of Support (Multi)')
                {
                    ApplicationArea = All;
                    Editable = false;
                    ShowCaption = false;
                }
                
                field(Migration3; 'Technician Group → Type of Technology')
                {
                    ApplicationArea = All;
                    Editable = false;
                    ShowCaption = false;
                }
            }
        }
    }

    actions
    {
        area(Processing)
        {
            action(RunMigration)
            {
                Caption = 'Run Migration';
                ApplicationArea = All;
                ToolTip = 'Runs the migration to copy existing single-selection values to the new multi-selection fields.';
                Image = Migration;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                
                trigger OnAction()
                var
                    OpportunityMigration: Codeunit "Opportunity Migration";
                begin
                    OpportunityMigration.MigrateAllOpportunities();
                end;
            }
        }
    }

    var
        InfoText: Text;

    trigger OnOpenPage()
    begin
        InfoText := 'This utility migrates existing single-selection values to the new multi-selection fields for all opportunities. ' +
                   'The original fields will remain unchanged. Only empty multi-selection fields will be populated. ' +
                   'This is a one-time migration that can be safely run multiple times.';
    end;
}
