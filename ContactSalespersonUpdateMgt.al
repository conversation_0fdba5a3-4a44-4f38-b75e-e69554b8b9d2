codeunit 50110 "Contact Salesperson Update Mgt"
{
    procedure ImportExcelFile(var ContactUpdateBuffer: Record "Contact Update Buffer")
    var
        ExcelBuffer: Record "Excel Buffer" temporary;
        FileManagement: Codeunit "File Management";
        IStream: InStream;
        FileName: Text[250];
        SheetName: Text[250];
        CustomerNameColumnNo: Integer;
        RowNo: Integer;
        TotalRows: Integer;
    begin
        ContactUpdateBuffer.DeleteAll();

        // Upload file
        if not UploadIntoStream('Import Excel File', '', 'Excel Files (*.xlsx)|*.xlsx|Excel Files (*.xls)|*.xls', FileName, IStream) then
            exit;

        // Import Excel data
        SheetName := ExcelBuffer.SelectSheetsNameStream(IStream);
        ExcelBuffer.Reset();
        ExcelBuffer.DeleteAll();
        ExcelBuffer.OpenBookStream(IStream, SheetName);
        ExcelBuffer.ReadSheet();

        // Find the "Customer Name" column
        CustomerNameColumnNo := FindCustomerNameColumn(ExcelBuffer);
        if CustomerNameColumnNo = 0 then
            Error('Could not find "Customer Name" column in the Excel file. Please ensure the file contains a column with this exact name.');

        // Count total rows for progress
        ExcelBuffer.SetRange("Row No.", 2, 999999); // Skip header row
        ExcelBuffer.SetRange("Column No.", CustomerNameColumnNo);
        TotalRows := ExcelBuffer.Count();

        if TotalRows = 0 then
            Error('No data found in the Customer Name column. Please check your Excel file.');

        // Import data into buffer
        RowNo := 0;
        if ExcelBuffer.FindSet() then
            repeat
                RowNo += 1;
                if ExcelBuffer."Cell Value as Text" <> '' then begin
                    ContactUpdateBuffer.Init();
                    ContactUpdateBuffer."Customer Name" := CopyStr(ExcelBuffer."Cell Value as Text", 1, MaxStrLen(ContactUpdateBuffer."Customer Name"));
                    if ContactUpdateBuffer.Insert() then;
                end;
            until ExcelBuffer.Next() = 0;

        if ContactUpdateBuffer.IsEmpty() then
            Error('No valid customer names found in the Excel file.');

        Message('Successfully imported %1 customer names from Excel file.', ContactUpdateBuffer.Count());
    end;

    local procedure FindCustomerNameColumn(var ExcelBuffer: Record "Excel Buffer"): Integer
    var
        ColumnNo: Integer;
    begin
        // Look for "Customer Name" column in the first row (header)
        ExcelBuffer.SetRange("Row No.", 1);
        if ExcelBuffer.FindSet() then
            repeat
                if UpperCase(ExcelBuffer."Cell Value as Text") = 'CUSTOMER NAME' then
                    exit(ExcelBuffer."Column No.");
            until ExcelBuffer.Next() = 0;

        exit(0);
    end;

    procedure ProcessContactUpdates(var ContactUpdateBuffer: Record "Contact Update Buffer"; SalespersonCode: Code[20]): Text
    var
        Contact: Record Contact;
        UpdatedContacts: Integer;
        UnassignedContacts: Integer;
        NotFoundCompanies: Integer;
        ResultText: Text;
        CompanyName: Text[100];
        ProgressDialog: Dialog;
        Counter: Integer;
        TotalRecords: Integer;
    begin
        UpdatedContacts := 0;
        UnassignedContacts := 0;
        NotFoundCompanies := 0;

        // Count total records for progress
        TotalRecords := ContactUpdateBuffer.Count();

        ProgressDialog.Open('Processing contact updates...\Progress: #1######');

        // First, unassign all contacts currently assigned to this salesperson
        Contact.SetRange("Salesperson Code", SalespersonCode);
        if Contact.FindSet(true) then
            repeat
                Contact."Salesperson Code" := '';
                Contact.Modify(true);
                UnassignedContacts += 1;
            until Contact.Next() = 0;

        // Then assign contacts based on Excel data
        Counter := 0;
        if ContactUpdateBuffer.FindSet() then
            repeat
                Counter += 1;
                ProgressDialog.Update(1, StrSubstNo('%1 of %2', Counter, TotalRecords));

                CompanyName := ContactUpdateBuffer."Customer Name";

                // Find contacts with matching company name
                Contact.Reset();
                Contact.SetRange("Company Name", CompanyName);
                if Contact.FindSet(true) then begin
                    repeat
                        Contact."Salesperson Code" := SalespersonCode;
                        Contact.Modify(true);
                        UpdatedContacts += 1;
                    until Contact.Next() = 0;
                end else
                    NotFoundCompanies += 1;

            until ContactUpdateBuffer.Next() = 0;

        ProgressDialog.Close();

        // Build result text
        ResultText := StrSubstNo('Contact update completed successfully!\' +
                                '\' +
                                'Summary:\' +
                                '- Contacts assigned to %1: %2\' +
                                '- Contacts previously unassigned: %3\' +
                                '- Company names not found: %4\' +
                                '\' +
                                'Total customer names processed: %5',
                                SalespersonCode,
                                UpdatedContacts,
                                UnassignedContacts,
                                NotFoundCompanies,
                                TotalRecords);

        exit(ResultText);
    end;
}
