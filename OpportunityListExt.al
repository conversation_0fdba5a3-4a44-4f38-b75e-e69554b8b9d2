reportextension 50100 "OpportunitiesReportsExt" extends "Opportunity - List"
{
    dataset
    {
        add(Opportunity)
        {
            column(Amount; "Amount") { }
            column(Cost__c; "Cost__c") { }
            column(Margin_Amount__c; "Margin_Amount__c") { }
            column(Margin_Percent__c; "Margin_Percent__c") { }
            column(Type_of_Project__c; "Type_of_Project__c") { }
            column(Type_Of_Support__c; "Type_Of_Support__c") { }
            column(Type_Of_Service__c; "Type_Of_Service__c") { }
            column(Type_Of_License__c; "Type_Of_License__c") { }
            column(PnL_Total_Hours__c; "PnL_Total_Hours__c") { }
            column(Partner_For_The_Project__c; "Partner_For_The_Project__c") { }
            column(Manufacturer__c; "Manufacturer__c") { }
            column(Serial_Number__c; "Serial_Number__c") { }
            column(Support_Start_Date__c; "Support_Start_Date__c") { }
            column(Support_End_Date__c; "Support_End_Date__c") { }
            column(Renewal_Date__c; "Renewal_Date__c") { }
            column(CloseDate; "CloseDate") { }
            column(Fiscal_Week__c; "Fiscal_Week__c") { }
            column(Fiscal_Year_Start__c; "Fiscal_Year_Start__c") { }
            column(Fiscal_Year_End__c; "Fiscal_Year_End__c") { }
            column(Tender_Offer_ID__c; "Tender_Offer_ID__c") { }
            column(Technician_Group__c; "Technician_Group__c") { }
            column(Technician_Assigned__c; "Technician_Assigned__c") { }
            column(Internal_Project_Name__c; "Internal_Project_Name__c") { }
            column(Follow_Up_Reminder__c; "Follow_Up_Reminder__c") { }
            column(ERP_Quote_Number__c; "ERP_Quote_Number__c") { }
            column(Partner_Quote_Number__c; "Partner_Quote_Number__c") { }
            column(Client_PO_Number__c; "Client_PO_Number__c") { }
            column(Internal_PO_Number__c; "Internal_PO_Number__c") { }
            column(Invoice__c; "Invoice__c") { }
            column(SalesforceOppId; "SalesforceOppId") { }
        }
    }

    // Remove the requestpage section entirely if you don't need to customize it
    // Or if you want to add your own options, don't reference existing fields
    requestpage
    {
        layout
        {
            addlast(Content)
            {
                group(CustomOptions)
                {
                    Caption = 'Custom Options';

                    field(ShowCustomFields; ShowCustomFields)
                    {
                        ApplicationArea = All;
                        Caption = 'Show Custom Fields';
                        ToolTip = 'Specifies if the report shows custom fields.';
                    }
                }
            }
        }
    }

    var
        ShowCustomFields: Boolean;
}