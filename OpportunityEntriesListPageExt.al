pageextension 50106 "Opportunity Entries List Ext" extends "Opportunity Entries" // Assuming "Opportunity Entries" is the correct page name
{
    actions
    {
        addlast(Processing)
        {
            action("Bulk Delete Opportunity Entries")
            {
                ApplicationArea = All;
                Caption = 'Bulk Delete Selected Entries';
                ToolTip = 'Deletes all selected opportunity entries. This action cannot be undone.';
                Image = Delete;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;

                trigger OnAction()
                var
                    OpportunityEntryRec: Record "Opportunity Entry"; // Use the correct record for Opportunity Entries
                    SelectedCount: Integer;
                    DeletedCount: Integer;
                    ConfirmMsg: Label 'You have selected %1 opportunity entries. Are you sure you want to delete them? This action cannot be undone.';
                    SuccessMsg: Label '%1 opportunity entries were deleted.';
                    NothingSelectedMsg: Label 'No opportunity entries were selected.';
                begin
                    CurrPage.SetSelectionFilter(OpportunityEntryRec);
                    SelectedCount := OpportunityEntryRec.Count();

                    if SelectedCount = 0 then begin
                        Message(NothingSelectedMsg);
                        exit;
                    end;

                    if Confirm(ConfirmMsg, false, SelectedCount) then begin
                        if OpportunityEntryRec.FindSet() then begin
                            repeat
                                // Add any specific checks here if needed before deleting an entry
                                if OpportunityEntryRec.Delete(true) then
                                    DeletedCount += 1;
                            until OpportunityEntryRec.Next() = 0;
                        end;
                        Message(SuccessMsg, DeletedCount);
                    end;
                end;
            }
        }
    }
}