permissionset 50100 "Time Bank Access"
{
    Caption = 'Customer Time Bank Access';
    Assignable = true;

    Permissions =
        tabledata "Customer Time Bank" = RIMD,
        table "Customer Time Bank" = X,
        page "Customer Time Bank Subform" = X;
}

permissionset 50101 "Tech Type Access"
{
    Caption = 'Tech Type Access';
    Assignable = true;

    Permissions =
        tabledata "Technology Type" = RIMD,
        table "Technology Type" = X,
        page "Technology Type Selection" = X;
}

permissionset 50102 "Support Type Access"
{
    Caption = 'Support Type Access';
    Assignable = true;

    Permissions =
        tabledata "Support Type" = RIMD,
        table "Support Type" = X,
        page "Support Type Selection" = X;
}

permissionset 50103 "Opp Type Access"
{
    Caption = 'Opportunity Type Access';
    Assignable = true;

    Permissions =
        tabledata "Opportunity Type" = RIMD,
        table "Opportunity Type" = X,
        page "Opportunity Type Selection" = X;
}

permissionset 50104 "Opp Migration Access"
{
    Caption = 'Opportunity Migration Access';
    Assignable = true;

    Permissions =
        codeunit "Opportunity Migration" = X,
        page "Migrate Opportunity Fields" = X;
}